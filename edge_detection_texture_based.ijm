// Edge Detection - Texture and Pattern Based Face Finder
// Uses texture analysis and pattern recognition to detect faces

// Configuration
WINDOW_SIZE = 7;
TEXTURE_THRESHOLD = 50;
MIN_FACE_AREA = 1000;
MAX_FACE_AREA = 50000;
CIRCULARITY_MIN = 0.3;
GAUSSIAN_BLUR = 1.0;

print("\\Clear");
print("=== Texture-Based Edge Detection ===");

// Get original image
if (nImages == 0) {
    showMessage("Error", "No image open");
    exit();
}
originalTitle = getTitle();
originalID = getImageID();

// Duplicate for processing
run("Duplicate...", "title=texture_processing");
processingID = getImageID();

// Pre-processing
if (GAUSSIAN_BLUR > 0) {
    run("Gaussian Blur...", "sigma=" + GAUSSIAN_BLUR);
}

// Method 1: Local Binary Pattern (simplified)
selectImage(processingID);
run("Duplicate...", "title=lbp");
lbpID = getImageID();

// Create a simplified LBP-like texture measure
// Compare center pixel with neighbors
run("32-bit");
width = getWidth();
height = getHeight();

// Create texture map
newImage("texture_map", "32-bit", width, height, 1);
textureMapID = getImageID();

selectImage(lbpID);
for (y = 1; y < height-1; y++) {
    for (x = 1; x < width-1; x++) {
        center = getPixel(x, y);
        
        // Compare with 8 neighbors
        pattern = 0;
        neighbors = newArray(
            getPixel(x-1, y-1), getPixel(x, y-1), getPixel(x+1, y-1),
            getPixel(x-1, y),                     getPixel(x+1, y),
            getPixel(x-1, y+1), getPixel(x, y+1), getPixel(x+1, y+1)
        );
        
        for (i = 0; i < 8; i++) {
            if (neighbors[i] > center) {
                pattern += pow(2, i);
            }
        }
        
        selectImage(textureMapID);
        setPixel(x, y, pattern);
        selectImage(lbpID);
    }
    if (y % 50 == 0) print("LBP processing: " + round(100*y/height) + "%");
}

selectImage(textureMapID);
run("8-bit");
setThreshold(TEXTURE_THRESHOLD, 255);
run("Convert to Mask");
run("Invert");
run("Close-");
run("Fill Holes");

run("Set Measurements...", "area centroid circularity redirect=None decimal=3");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-1.0 show=Outlines display exclude clear add");

print("Local Binary Pattern found " + roiManager("count") + " potential faces");

// Method 2: Gray Level Co-occurrence Matrix (GLCM) features
selectImage(processingID);
run("Duplicate...", "title=glcm");
glcmID = getImageID();

// Calculate local entropy (texture measure)
run("Duplicate...", "title=entropy_map");
entropyID = getImageID();

// Use ImageJ's entropy filter if available, otherwise use variance
if (indexOf(getList("window"), "Entropy") >= 0) {
    run("Filters", "filter=Entropy radius=" + floor(WINDOW_SIZE/2));
} else {
    // Fallback: use variance as texture measure
    run("Variance...", "radius=" + floor(WINDOW_SIZE/2));
}

run("8-bit");
setThreshold(TEXTURE_THRESHOLD, 255);
run("Convert to Mask");
run("Invert");
run("Close-");
run("Fill Holes");

roiManager("reset");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-1.0 show=Outlines display exclude clear add");

print("GLCM/Entropy method found " + roiManager("count") + " potential faces");

// Method 3: Gabor-like Filters
selectImage(processingID);
run("Duplicate...", "title=gabor");
gaborID = getImageID();
run("32-bit");

// Create multiple orientation filters
orientations = newArray(0, 45, 90, 135);
gaborResponses = newArray(4);

for (i = 0; i < 4; i++) {
    selectImage(gaborID);
    run("Duplicate...", "title=gabor_" + orientations[i]);
    gaborResponses[i] = getImageID();
    
    // Simplified Gabor-like filter using directional derivatives
    if (orientations[i] == 0) {
        run("Convolve...", "text1=[-1 0 1\n-2 0 2\n-1 0 1\n]");
    } else if (orientations[i] == 45) {
        run("Convolve...", "text1=[-2 -1 0\n-1 0 1\n0 1 2\n]");
    } else if (orientations[i] == 90) {
        run("Convolve...", "text1=[-1 -2 -1\n0 0 0\n1 2 1\n]");
    } else if (orientations[i] == 135) {
        run("Convolve...", "text1=[0 1 2\n-1 0 1\n-2 -1 0\n]");
    }
    run("Abs");
}

// Combine all Gabor responses
selectImage(gaborResponses[0]);
for (i = 1; i < 4; i++) {
    imageCalculator("Max", "gabor_" + orientations[0], "gabor_" + orientations[i]);
}
rename("gabor_combined");
gaborCombinedID = getImageID();

run("8-bit");
setThreshold(TEXTURE_THRESHOLD, 255);
run("Convert to Mask");
run("Invert");
run("Close-");
run("Fill Holes");

roiManager("reset");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-1.0 show=Outlines display exclude clear add");

print("Gabor-like filters found " + roiManager("count") + " potential faces");

// Method 4: Fractal Dimension
selectImage(processingID);
run("Duplicate...", "title=fractal");
fractalID = getImageID();

// Calculate local fractal dimension using box-counting method (simplified)
// This is a rough approximation
run("Find Edges");
run("Gaussian Blur...", "sigma=1");
setThreshold(50, 255);
run("Convert to Mask");
run("Invert");
run("Close-");
run("Fill Holes");

roiManager("reset");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-1.0 show=Outlines display exclude clear add");

print("Fractal-based method found " + roiManager("count") + " potential faces");

// Display results
selectImage(originalID);
roiManager("Show All");
roiManager("Show All with labels");

// Clean up
selectImage(processingID); close();
selectImage(lbpID); close();
selectImage(textureMapID); close();
selectImage(glcmID); close();
selectImage(entropyID); close();
selectImage(gaborID); close();
for (i = 0; i < 4; i++) {
    selectImage(gaborResponses[i]); close();
}
selectImage(gaborCombinedID); close();
selectImage(fractalID); close();

print("=== Texture-Based Edge Detection Complete ===");
print("Adjust TEXTURE_THRESHOLD (" + TEXTURE_THRESHOLD + ") if needed");
print("Adjust WINDOW_SIZE (" + WINDOW_SIZE + ") for different texture scales");
