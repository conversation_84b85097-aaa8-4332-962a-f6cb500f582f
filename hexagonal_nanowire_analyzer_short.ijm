// Hexagonal Nanowire Face Analyzer
// Configuration
// ANGLE_TOL: Tolerance for angle deviation from 120°
// PARALLEL_TOL: Tolerance for parallelism deviation of opposite edges
// MIN_SIZE: Minimum size of face to be detected (in microns)
// SHARP_THRESH: Threshold for corner sharpness (0.0 - 1.0)
// MIN_CORNERS: Minimum number of corners to consider the shape hexagonal
// OUTLIER_TOL: Tolerance for number of points outside hexagon 
ANGLE_TOL = 15; PARALLEL_TOL = 10; MIN_SIZE = 250; SHARP_THRESH = 0.5; MIN_CORNERS = 4; OUTLIER_TOL = 2;
pixels = 603; 
microns = 500; 
pixelsPerMicron = pixels/microns;

print("\\Clear"); print("=== Hexagonal Nanowire Analyzer ===");
if (nImages == 0) { showMessage("Error", "No image open"); exit(); }
original = getImageID();
run("Set Scale...", "  distance=" + 1 + " known=" + 1 + " unit=pixel");

// ============================================
// Face Detection - If faces are not recognized correctly,
// please vary the used methods in the following section
// ============================================
print("\n--- Face Detection ---");
selectImage(original); run("32-bit"); run("Duplicate...", "title=Detection"); detectionID = getImageID();
// Enhance contrast and denoise afterwards
run("Enhance Local Contrast (CLAHE)", "blocksize=127 histogram=256 maximum=3 mask=*None*"); run("ROF Denoise", "theta=25");
// Use logarithmic stretch and exponential stretch to better separate bright faces from background
run("Macro...", "code=v=v*log(v)"); setOption("ScaleConversions", true); run("8-bit"); run("Exp");
// Thresholding
run("Auto Threshold", "method=MaxEntropy white");
// Clean up small artifacts and fill faces
run("Fill Holes"); run("Open"); run("Close-");
// Find faces with size larger than minimum size
run("Set Measurements...", "area centroid bounding redirect=None decimal=3");
run("Analyze Particles...", "size=" + MIN_SIZE * pixelsPerMicron * pixelsPerMicron + "-Infinity show=Overlay add display exclude clear");
nFaces = nResults; print("Detected " + nFaces + " faces");
if (nFaces == 0) { showMessage("No Faces", "No faces detected"); exit(); }

// ============================================
// END OF FACE DETECTION
// ============================================

// User Selection of face to be fitted
selectImage(original); roiManager("Show All with labels");
waitForUser("Face Selection", "Select ROI around hexagonal face");
if (selectionType() == -1) { showMessage("Error", "No ROI selected"); exit(); }
originalArea = getValue("Area"); run("Remove Overlay"); roiManager("Reset");
roiManager("Add"); roiManager("Select", 0); roiManager("Rename", "Original_Selection");

// ============================================
// Corner Analysis
// ============================================
// Ensure we have the original selection active
print("\n--- Corner Analysis ---");
roiManager("Select", 0);
getSelectionCoordinates(xCoords, yCoords);
if (xCoords.length < 6) { showMessage("Error", "Too few points"); exit(); }

// Get centroid of the selected face using built-in measurements
run("Set Measurements...", "centroid redirect=None decimal=3");
run("Measure"); centroidX = getResult("X", nResults-1); centroidY = getResult("Y", nResults-1);
run("Clear Results");


// Generate convex hull and maintain selection
run("Convex Hull");
roiManager("Update");  // Update the ROI in manager with convex hull
getSelectionCoordinates(hullX, hullY); nHullPoints = hullX.length;
print("Convex hull has " + nHullPoints + " points");
angles = newArray(nHullPoints); distances = newArray(nHullPoints);
for (i = 0; i < nHullPoints; i++) {
    dx = hullX[i] - centroidX; dy = hullY[i] - centroidY;
    angles[i] = atan2(dy, dx) * 180 / PI; if (angles[i] < 0) angles[i] += 360;
    distances[i] = sqrt(dx*dx + dy*dy);
    }

// Sort by angle
for (i = 0; i < nHullPoints - 1; i++) {
    for (j = i + 1; j < nHullPoints; j++) {
        if (angles[i] > angles[j]) {
            temp = angles[i]; angles[i] = angles[j]; angles[j] = temp;
            temp = hullX[i]; hullX[i] = hullX[j]; hullX[j] = temp;
            temp = hullY[i]; hullY[i] = hullY[j]; hullY[j] = temp;
            temp = distances[i]; distances[i] = distances[j]; distances[j] = temp;
        }
    }
}

avgRadius = 0; for (i = 0; i < nHullPoints; i++) avgRadius += distances[i]; avgRadius /= nHullPoints;

// Corner sharpness analysis, i.e. how much do the edges bend at each point (comparing to points around it)
curvatures = newArray(nHullPoints);
cornerIndices = newArray(nHullPoints);
for (i = 0; i < nHullPoints; i++) cornerIndices[i] = i;

// Calculate curvatures using multiple window sizes and take the average
for (i = 0; i < nHullPoints; i++) {
    avgCurvature = 0;

    // Test different window sizes (1, 2, 3 points away from the point that is being tested)
    for (windowSize = 1; windowSize <= 3; windowSize++) {
        prev = (i - windowSize + nHullPoints) % nHullPoints;
        next = (i + windowSize) % nHullPoints;

        v1x = hullX[i] - hullX[prev]; v1y = hullY[i] - hullY[prev];
        v2x = hullX[next] - hullX[i]; v2y = hullY[next] - hullY[i];

        // Normalize vectors
        len1 = sqrt(v1x*v1x + v1y*v1y); len2 = sqrt(v2x*v2x + v2y*v2y);
        if (len1 > 0 && len2 > 0) {
            v1x /= len1; v1y /= len1; v2x /= len2; v2y /= len2;

            // Calculate angle change
            angle1 = atan2(v1y, v1x); angle2 = atan2(v2y, v2x);
            angleDiff = angle2 - angle1;
            while (angleDiff > PI) angleDiff -= 2 * PI;
            while (angleDiff < -PI) angleDiff += 2 * PI;

            curvature = abs(angleDiff);
            avgCurvature += curvature/3;
        }
    }
    curvatures[i] = avgCurvature;
}


// Find the 6 sharpest corners with proper separation
selectedCorners = newArray();
selectedCount = 0; maxCurvature = 0;
MIN_CORNER_SEPARATION = maxOf(2, floor(nHullPoints / 8));

for (i = 0; i < nHullPoints; i++) if (curvatures[i] > maxCurvature) maxCurvature = curvatures[i];

for (i = 0; i < nHullPoints; i++) {
    if (maxCurvature > 0 && curvatures[i] / maxCurvature > SHARP_THRESH) {
        prev = (i - 1 + nHullPoints) % nHullPoints;
        next = (i + 1) % nHullPoints;
        if (curvatures[i] >= curvatures[prev] && curvatures[i] >= curvatures[next]) {

            // Check if this corner is too close to existing corners
            tooClose = false;
            replaceIdx = -1;
            for (j = 0; j < selectedCount; j++) {
                separation = abs(selectedCorners[j] - i);
                if (separation > nHullPoints / 2) separation = nHullPoints - separation; // Handle wrap-around

                if (separation <= MIN_CORNER_SEPARATION) {
                    tooClose = true;
                    if (curvatures[i] > curvatures[selectedCorners[j]]) {
                        replaceIdx = j; // Replace weaker corner
                    }
                    break;
                }
            }

            if (!tooClose) {
                // Add new corner if well-separated
                selectedCorners = Array.concat(selectedCorners, i);
                selectedCount++;
            } else if (replaceIdx >= 0) {
                // Replace weaker corner
                selectedCorners[replaceIdx] = i;
            }
        }
    }
}

print("Found " + selectedCount + " well-separated corners (min separation: " + MIN_CORNER_SEPARATION + ")");


// Debug output
for (i = 0; i < minOf(selectedCount, 6); i++) {
    idx = selectedCorners[i];
    print("Corner " + (i+1) + ": hull point " + idx + ", curvature=" + d2s(curvatures[idx], 4));
}


// Get edge points from original selection for scoring how well the fitted hexagon matches the original shape
roiManager("Select", 0); // Select original ROI
if (selectionType() == -1) {
    print("ERROR: No selection active for edge point extraction");
    // Try to restore selection from ROI manager
    roiManager("Show All");
    roiManager("Select", 0);
    if (selectionType() == -1) {
        print("CRITICAL: Cannot restore selection, using hull points as fallback");
        originalEdgeX = Array.copy(hullX); originalEdgeY = Array.copy(hullY);
        nEdgePoints = nHullPoints;
        allEdgeX = Array.copy(hullX); allEdgeY = Array.copy(hullY);
        nAllEdgePoints = nHullPoints;
    } else {
        getSelectionCoordinates(originalEdgeX, originalEdgeY);
        nEdgePoints = originalEdgeX.length;
        allEdgeX = Array.copy(originalEdgeX); allEdgeY = Array.copy(originalEdgeY);
        nAllEdgePoints = nEdgePoints;
    }
} else {
    // Get all edge points from the current selection
    getSelectionCoordinates(allEdgeX, allEdgeY);
    nAllEdgePoints = allEdgeX.length;

    // Also populate originalEdgeX/Y with the same coordinates
    originalEdgeX = Array.copy(allEdgeX);
    originalEdgeY = Array.copy(allEdgeY);
    nEdgePoints = nAllEdgePoints;


// ============================================
// Hexagon Fitting
// ============================================
// Determine which mode of hexagon fitting to use:
// few good corners found: use regular hexagon approach
// many good corners found: use corner-based fitting and use sector-based fallback if needed
print("\n--- Hexagon Fitting ---");

regularHexagonMode = !(selectedCount >= MIN_CORNERS);

// These are the arrays where the final hexagon vertices will be stored
hexVerticesX = newArray(6); hexVerticesY = newArray(6);

if (regularHexagonMode) {
    print("Using regular hexagon approach");
    // Regular hexagon approach
    bestScore = -999999; bestRotation = 0;
    for (testRotation = 0; testRotation < 360; testRotation += 1.0) {
        score = calculateRotationScore(testRotation, centroidX, centroidY, avgRadius, originalEdgeX, originalEdgeY, nAllEdgePoints);
        if (score > bestScore) { bestScore = score; bestRotation = testRotation; }
    }
    
    // Fine scan
    for (testRotation = bestRotation - 5; testRotation <= bestRotation + 5; testRotation += 0.1) {
        score = calculateRotationScore(testRotation, centroidX, centroidY, avgRadius, originalEdgeX, originalEdgeY, nAllEdgePoints);
        if (score > bestScore) { bestScore = score; bestRotation = testRotation; }
    }
    
    // Generate vertices
    for (i = 0; i < 6; i++) {
        angle = (i * 60 + bestRotation) * PI / 180;
        hexVerticesX[i] = centroidX + avgRadius * cos(angle);
        hexVerticesY[i] = centroidY + avgRadius * sin(angle);
    }


    // Expand to contain as many points as possible
    expansion = 0; maxExpansions = 30;
    while (expansion < maxExpansions) {
        outsideCount = 0;
        for (i = 0; i < nAllEdgePoints; i++) {
            if (!isPointInPolygon(originalEdgeX[i], originalEdgeY[i], hexVerticesX, hexVerticesY, 6)) outsideCount++;
        }
        if (outsideCount / nAllEdgePoints <= OUTLIER_TOL) break;
        for (i = 0; i < 6; i++) {
            dx = hexVerticesX[i] - centroidX; dy = hexVerticesY[i] - centroidY;
            length = sqrt(dx*dx + dy*dy);
            if (length > 0) {
                scale = (length + 0.15) / length;
                hexVerticesX[i] = centroidX + dx * scale;
                hexVerticesY[i] = centroidY + dy * scale;
            }
        }
        expansion++;
    }
} else {
    print("Using adaptive corner-based approach");
    // Use the already identified sharp corners from enhanced detection

    if (selectedCount >= 6) {
        // Sort the selected corners by their position along the hull (preserving order)
        // First, sort by hull index to maintain proper sequence
        for (i = 0; i < selectedCount - 1; i++) {
            for (j = i + 1; j < selectedCount; j++) {
                if (selectedCorners[i] > selectedCorners[j]) {
                    temp = selectedCorners[i]; selectedCorners[i] = selectedCorners[j]; selectedCorners[j] = temp;
                }
            }
        }

        // Take the best 6 corners, evenly distributed if possible
        if (selectedCount > 6) {
            // Select 6 corners with best distribution
            finalCorners = newArray(6);
            step = selectedCount / 6.0;
            for (i = 0; i < 6; i++) {
                idx = floor(i * step);
                if (idx >= selectedCount) idx = selectedCount - 1;
                finalCorners[i] = selectedCorners[idx];
            }
            selectedCorners = finalCorners;
            selectedCount = 6;
        }

        // Assign vertices in hull order (this preserves proper hexagon shape)
        for (i = 0; i < selectedCount; i++) {
            hexVerticesX[i] = hullX[selectedCorners[i]];
            hexVerticesY[i] = hullY[selectedCorners[i]];
        }

        // If we have fewer than 6 corners, fill the rest with interpolated positions
        if (selectedCount < 6) {
            for (i = selectedCount; i < 6; i++) {
                targetAngle = i * 60 * PI / 180;
                hexVerticesX[i] = centroidX + avgRadius * cos(targetAngle);
                hexVerticesY[i] = centroidY + avgRadius * sin(targetAngle);
            }
        }
    } else {
        // Hybrid approach: use found corners and fill missing sectors
        IJ.log("Using hybrid approach: " + selectedCount + " corners found, filling missing sectors");

        // Find the sharpest corner to center the first sector around it
        best_corner_index = -1; maxCurvature = 0;
        for (i = 0; i < selectedCount; i++) {
            cornerIdx = selectedCorners[i];
            if (curvatures[cornerIdx] > maxCurvature) {
                maxCurvature = curvatures[cornerIdx];
                best_corner_index = cornerIdx;
            }
        }

        // If no corners found, fall back to global sharpest
        if (best_corner_index < 0) {
            for (i = 0; i < nHullPoints; i++) {
                if (curvatures[i] > maxCurvature) {
                    maxCurvature = curvatures[i];
                    best_corner_index = i;
                }
            }
        }
        if (best_corner_index < 0) best_corner_index = 0;

        // Center the first sector around the sharpest corner (±30° from corner)
        sharpestCornerAngle = angles[best_corner_index];
        sectorCenterOffset = sharpestCornerAngle; // First sector centered on sharpest corner

        IJ.log("Centering sectors around sharpest corner at angle " + d2s(sharpestCornerAngle, 1) + "°");

        // Initialize sector occupancy tracking
        sectorOccupied = newArray(6);
        for (i = 0; i < 6; i++) sectorOccupied[i] = false;

        // Place found corners in their appropriate sectors (relative to sharpest corner)
        for (i = 0; i < selectedCount; i++) {
            cornerIdx = selectedCorners[i];
            cornerAngle = angles[cornerIdx];

            // Calculate relative angle from the sharpest corner
            relativeAngle = cornerAngle - sectorCenterOffset;
            if (relativeAngle < 0) relativeAngle += 360;
            if (relativeAngle >= 360) relativeAngle -= 360;

            // Determine which sector this corner belongs to (60° sectors, first centered on sharpest)
            sectorNum = floor((relativeAngle + 30) / 60); // +30 to center sectors
            if (sectorNum >= 6) sectorNum = 0; // Handle wrap-around

            hexVerticesX[sectorNum] = hullX[cornerIdx];
            hexVerticesY[sectorNum] = hullY[cornerIdx];
            sectorOccupied[sectorNum] = true;

            IJ.log("Corner " + (i+1) + ": hull point " + cornerIdx + " at angle " + d2s(cornerAngle, 1) + "° (relative: " + d2s(relativeAngle, 1) + "°) assigned to sector " + (sectorNum + 1));
        }

        // Fill empty sectors using the same sector centering approach
        emptySectorCount = 0;
        for (sector = 0; sector < 6; sector++) {
            if (!sectorOccupied[sector]) {
                emptySectorCount++;

                // Calculate sector boundaries relative to the sharpest corner
                // Each sector is 60° wide, centered around: sharpest + (sector * 60°)
                sectorCenter = sectorCenterOffset + (sector * 60);
                sectorStart = sectorCenter - 30;
                sectorEnd = sectorCenter + 30;

                // Handle wrap-around for sector boundaries
                if (sectorStart < 0) sectorStart += 360;
                if (sectorEnd >= 360) sectorEnd -= 360;
                if (sectorStart >= 360) sectorStart -= 360;

                bestPointIdx = -1; maxDist = 0;
                for (i = 0; i < nHullPoints; i++) {
                    angle = angles[i];

                    // Check if point falls within this sector
                    inSector = false;
                    if (sectorEnd < sectorStart) { // Sector crosses 0°/360°
                        if (angle >= sectorStart || angle <= sectorEnd) {
                            inSector = true;
                        }
                    } else { // Normal sector
                        if (angle >= sectorStart && angle <= sectorEnd) {
                            inSector = true;
                        }
                    }

                    if (inSector && distances[i] > maxDist) {
                        maxDist = distances[i]; bestPointIdx = i;
                    }
                }

                if (bestPointIdx >= 0) {
                    hexVerticesX[sector] = hullX[bestPointIdx];
                    hexVerticesY[sector] = hullY[bestPointIdx];
                    IJ.log("Empty sector " + (sector + 1) + ": using hull point " + bestPointIdx + " at angle " + d2s(angles[bestPointIdx], 1) + "° (sector: " + d2s(sectorStart, 1) + "°-" + d2s(sectorEnd, 1) + "°)");
                } else {
                    // Use sector center for interpolation
                    targetAngle = sectorCenter;
                    if (targetAngle >= 360) targetAngle -= 360;
                    if (targetAngle < 0) targetAngle += 360;
                    targetAngle = targetAngle * PI / 180;
                    hexVerticesX[sector] = centroidX + avgRadius * cos(targetAngle);
                    hexVerticesY[sector] = centroidY + avgRadius * sin(targetAngle);
                    IJ.log("Empty sector " + (sector + 1) + ": using interpolated point at angle " + d2s(sectorCenter, 1) + "°");
                }
            }
        }

        IJ.log("Filled " + emptySectorCount + " empty sectors using sector-based approach");
    }
}
// Expand to contain as many points as possible
    expansion = 0; maxExpansions = 30;
    while (expansion < maxExpansions) {
        outsideCount = 0;
        for (i = 0; i < nAllEdgePoints; i++) {
            if (!isPointInPolygon(originalEdgeX[i], originalEdgeY[i], hexVerticesX, hexVerticesY, 6)) outsideCount++;
        }
        if (outsideCount < nAllEdgePoints-OUTLIER_TOL) break;
        for (i = 0; i < 6; i++) {
            dx = hexVerticesX[i] - centroidX; dy = hexVerticesY[i] - centroidY;
            length = sqrt(dx*dx + dy*dy);
            if (length > 0) {
                scale = (length + 0.15) / length;
                hexVerticesX[i] = centroidX + dx * scale;
                hexVerticesY[i] = centroidY + dy * scale;
            }
        }
        expansion++;


// ===============================================
// Hexagon Validation and Hexagonality Measurement
// ===============================================
print("\n--- Hexagon Validation and Hexagonality Measurement ---");


// Create fitted hexagon and validate
makeSelection("polygon", hexVerticesX, hexVerticesY); fittedArea = getValue("Area");
validHexagon = true;

// Angle validation
maxAngleError = 0; maxParallelismError = 0;
for (i = 0; i < 6; i++) {
    p1 = i; p2 = (i + 1) % 6; p3 = (i + 2) % 6;
    v1x = hexVerticesX[p1] - hexVerticesX[p2]; v1y = hexVerticesY[p1] - hexVerticesY[p2];
    v2x = hexVerticesX[p3] - hexVerticesX[p2]; v2y = hexVerticesY[p3] - hexVerticesY[p2];
    dot = v1x * v2x + v1y * v2y; mag1 = sqrt(v1x * v1x + v1y * v1y); mag2 = sqrt(v2x * v2x + v2y * v2y);
    if (mag1 > 0 && mag2 > 0) {
        cosAngle = dot / (mag1 * mag2); if (cosAngle > 1) cosAngle = 1; if (cosAngle < -1) cosAngle = -1;
        angle = acos(cosAngle) * 180 / PI; angleError = abs(angle - 120);
        if (angleError > ANGLE_TOL) validHexagon = false;
        if (angleError > maxAngleError) maxAngleError = angleError;
    }
    IJ.log("Angle " + (i+1) + ": " + d2s(angle, 1) + "° (error: " + d2s(angleError, 1) + "°)");
}

// Parallel line validation
for (i = 0; i < 3; i++) {
    side1_start = i; side1_end = (i + 1) % 6; side2_start = (i + 3) % 6; side2_end = (i + 4) % 6;
    dx1 = hexVerticesX[side1_end] - hexVerticesX[side1_start]; dy1 = hexVerticesY[side1_end] - hexVerticesY[side1_start];
    dx2 = hexVerticesX[side2_end] - hexVerticesX[side2_start]; dy2 = hexVerticesY[side2_end] - hexVerticesY[side2_start];
    angle1 = atan2(dy1, dx1) * 180 / PI; angle2 = atan2(dy2, dx2) * 180 / PI;
    if (angle1 < 0) angle1 += 180; if (angle2 < 0) angle2 += 180;
    angleDiff = abs(angle1 - angle2); if (angleDiff > 90) angleDiff = 180 - angleDiff;
    if (angleDiff > PARALLEL_TOL) validHexagon = false;
    if (angleDiff > maxParallelismError) maxParallelismError = angleDiff;
    IJ.log("Parallelism " + (i+1) + ": " + d2s(angleDiff, 1) + "°");
}

// Hexagonality measurement - calculate inner circle radius (distance from center to edges)
avgRadius = 0;
for (i = 0; i < 6; i++) {
    // Get the two vertices that define this edge
    x1 = hexVerticesX[i];
    y1 = hexVerticesY[i];
    x2 = hexVerticesX[(i + 1) % 6];
    y2 = hexVerticesY[(i + 1) % 6];

    // Calculate perpendicular distance from centroid to this edge
    // Using formula: |ax + by + c| / sqrt(a² + b²) where line is ax + by + c = 0
    // Line equation: (y2-y1)x - (x2-x1)y + (x2-x1)y1 - (y2-y1)x1 = 0
    a = y2 - y1;
    b = -(x2 - x1);
    c = (x2 - x1) * y1 - (y2 - y1) * x1;

    // Distance from centroid to edge
    edgeDistance = abs(a * centroidX + b * centroidY + c) / sqrt(a * a + b * b);
    avgRadius += edgeDistance / 6;
}
innerCircleArea = PI * avgRadius * avgRadius;
hexagonality = 1 - (originalArea - fittedArea) / (fittedArea - innerCircleArea);
areaDifference = abs(fittedArea - originalArea); areaRatio = fittedArea / originalArea;

// Results
print("\n=== Results ===");
print("Original area: " + d2s(originalArea, 2) + " | Fitted hexagon area: " + d2s(fittedArea, 2) + " | Ratio: " + d2s(areaRatio, 3));
print("Inscribed circle area: " + d2s(innerCircleArea, 2));
print("Original area - fitted area: " + d2s(areaDifference, 2));
print("Fitted area - inscribed circle area: " + d2s(fittedArea - innerCircleArea, 2));
print("Hexagonality: " + d2s(hexagonality, 3));
print("Hexagon validation: max. allowed angle deviation: " + ANGLE_TOL + "°, max. allowed parallelism deviation: " + PARALLEL_TOL + "°");
if (validHexagon) {
    print("Hexagon validation: PASSED (max. angle deviation: " + d2s(maxAngleError, 1) + "°, max. parallelism error: " + d2s(maxParallelismError, 1) + "°)");
}
else {
    print("Hexagon validation: FAILED (max. angle deviation: " + d2s(maxAngleError, 1) + "°, max. parallelism error: " + d2s(maxParallelismError, 1) + "°)");
}


// Cleanup and display
run("Remove Overlay"); roiManager("Reset"); roiManager("Add"); roiManager("Select", 0);
roiManager("Rename", "Fitted_Hexagon"); roiManager("Show All with labels"); roiManager("Deselect");
// Close detection image
selectImage(detectionID); close();
selectImage(original); run("Select None");
print("=== Analysis Complete ===");

// Helper Functions

function isPointInPolygon(px, py, polyX, polyY, numVertices) {
    inside = false; j = numVertices - 1;
    for (i = 0; i < numVertices; i++) {
        if (((polyY[i] > py) != (polyY[j] > py)) && (px < (polyX[j] - polyX[i]) * (py - polyY[i]) / (polyY[j] - polyY[i]) + polyX[i]))
            inside = !inside;
        j = i;
    }
    return inside;
}

function calculateEnhancedScore(testVerticesX, testVerticesY, edgePointsX, edgePointsY, numEdgePoints) {
    // Component 1: Containment score (how many edge points are inside)
    insideCount = 0;
    for (i = 0; i < numEdgePoints; i++) {
        if (isPointInPolygon(edgePointsX[i], edgePointsY[i], testVerticesX, testVerticesY, 6)) insideCount++;
    }
    containmentScore = (insideCount / numEdgePoints);

    // Component 2: Edge proximity score (how close are edge points to hexagon boundary)
    totalProximity = 0;
    for (i = 0; i < numEdgePoints; i++) {
        minDistToEdge_sq = 999999;
        px = edgePointsX[i]; py = edgePointsY[i];

        // Check distance to each hexagon edge
        for (j = 0; j < 6; j++) {
            x1 = testVerticesX[j]; y1 = testVerticesY[j];
            x2 = testVerticesX[(j + 1) % 6]; y2 = testVerticesY[(j + 1) % 6];

            // Distance from point to line segment
            A = px - x1; B = py - y1; C = x2 - x1; D = y2 - y1;
            dot = A * C + B * D; lenSq = C * C + D * D;

            if (lenSq > 0) {
                param = dot / lenSq;
                if (param < 0) { xx = x1; yy = y1; }
                else if (param > 1) { xx = x2; yy = y2; }
                else { xx = x1 + param * C; yy = y1 + param * D; }

                dist_sq = (px - xx) * (px - xx) + (py - yy) * (py - yy);
                if (dist_sq < minDistToEdge_sq) minDistToEdge_sq = dist_sq;
            }
        }
        totalProximity += sqrt(minDistToEdge_sq);
    }
    proximityScore = -totalProximity / numEdgePoints; // Negative because we want to minimize distance

    // Component 3: Hexagon regularity score
    regularityScore = calculateRegularityScore(testVerticesX, testVerticesY);

    // Combined score with weights
    finalScore = containmentScore * 1000 + proximityScore * 10.0 + regularityScore * 1;
    IJ.log("containmentScore: " + containmentScore + "  proximityScore: " + proximityScore + "  regularityScore: " + regularityScore);
    return finalScore;
}

function calculateRegularityScore(verticesX, verticesY) {    
    // Parallelity of opposing sides
    parallelityScore = 0;
    for (i = 0; i < 3; i++) {
        side1_start = i; side1_end = (i + 1) % 6; side2_start = (i + 3) % 6; side2_end = (i + 4) % 6;
        dx1 = verticesX[side1_end] - verticesX[side1_start]; dy1 = verticesY[side1_end] - verticesY[side1_start];
        dx2 = verticesX[side2_end] - verticesX[side2_start]; dy2 = verticesY[side2_end] - verticesY[side2_start];
        dot = dx1 * dx2 + dy1 * dy2;
        mag1 = sqrt(dx1 * dx1 + dy1 * dy1); mag2 = sqrt(dx2 * dx2 + dy2 * dy2);
        if (mag1 > 0 && mag2 > 0) {
            cosAngle = dot / (mag1 * mag2);
            if (cosAngle > 1) cosAngle = 1; if (cosAngle < -1) cosAngle = -1;
            angle = acos(cosAngle) * 180 / PI;
            parallelityScore += (180 - abs(angle)); // Reward for being parallel (180° apart)
        }
    }

    // Angle regularity (should be close to 120 degrees)
    angleScore = 0;
    for (i = 0; i < 6; i++) {
        prev = (i - 1 + 6) % 6; next = (i + 1) % 6;
        v1x = verticesX[prev] - verticesX[i]; v1y = verticesY[prev] - verticesY[i];
        v2x = verticesX[next] - verticesX[i]; v2y = verticesY[next] - verticesY[i];

        dot = v1x * v2x + v1y * v2y;
        mag1 = sqrt(v1x * v1x + v1y * v1y); mag2 = sqrt(v2x * v2x + v2y * v2y);

        if (mag1 > 0 && mag2 > 0) {
            cosAngle = dot / (mag1 * mag2);
            if (cosAngle > 1) cosAngle = 1; if (cosAngle < -1) cosAngle = -1;
            angle = acos(cosAngle) * 180 / PI;
            angleError = abs(angle - 120);
            angleScore -= angleError; // Penalty for deviation from 120 degrees
        }
    }
    IJ.log("parallelityScore: " + parallelityScore + " angle score: " + angleScore);
    return (parallelityScore * 2 + angleScore);
}

function isHexagonConstraintViolated(verticesX, verticesY) {
    MAX_ANGLE_DEVIATION = 5; // More lenient than before
    MAX_SIDE_RATIO = 3.0; // Maximum ratio between longest and shortest side

    // Check angle constraints
    for (i = 0; i < 6; i++) {
        prev = (i - 1 + 6) % 6; next = (i + 1) % 6;
        v1x = verticesX[prev] - verticesX[i]; v1y = verticesY[prev] - verticesY[i];
        v2x = verticesX[next] - verticesX[i]; v2y = verticesY[next] - verticesY[i];

        dot = v1x * v2x + v1y * v2y;
        mag1 = sqrt(v1x * v1x + v1y * v1y); mag2 = sqrt(v2x * v2x + v2y * v2y);

        if (mag1 > 0 && mag2 > 0) {
            cosAngle = dot / (mag1 * mag2);
            if (cosAngle > 1) cosAngle = 1; if (cosAngle < -1) cosAngle = -1;
            angle = acos(cosAngle) * 180 / PI;
            angleError = abs(angle - 120);
            if (angleError > MAX_ANGLE_DEVIATION) return true;
        }
    }

    // Check side length ratios
    minSide = 999999; maxSide = 0;
    for (i = 0; i < 6; i++) {
        dx = verticesX[(i + 1) % 6] - verticesX[i];
        dy = verticesY[(i + 1) % 6] - verticesY[i];
        sideLength = sqrt(dx * dx + dy * dy);
        if (sideLength < minSide) minSide = sideLength;
        if (sideLength > maxSide) maxSide = sideLength;
    }

    if (minSide > 0 && maxSide / minSide > MAX_SIDE_RATIO) return true;

    return false;
}

function calculateRotationScore(rotation, centroidX, centroidY, radius, edgePointsX, edgePointsY, numEdgePoints) {
    // Generate hexagon vertices based on rotation
    hexVerticesX = newArray(6); hexVerticesY = newArray(6);
    for (i = 0; i < 6; i++) {
        angle = (i * 60 + rotation) * PI / 180;
        hexVerticesX[i] = centroidX + radius * cos(angle);
        hexVerticesY[i] = centroidY + radius * sin(angle);
    }

    // Calculate score using enhanced scoring function
    return calculateEnhancedScore(hexVerticesX, hexVerticesY, edgePointsX, edgePointsY, numEdgePoints);
}