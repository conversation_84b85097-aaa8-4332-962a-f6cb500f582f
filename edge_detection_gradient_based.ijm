// Edge Detection - Gradient Based Face Finder
// Uses Sobel/gradient operators to detect edges and find closed contours

// Configuration
GAUSSIAN_SIGMA = 1.5;
GRADIENT_THRESHOLD = 20;
MIN_FACE_AREA = 1000;
MAX_FACE_AREA = 50000;
CIRCULARITY_MIN = 0.3;
CIRCULARITY_MAX = 1.0;

print("\\Clear");
print("=== Gradient-Based Edge Detection ===");

// Get original image
if (nImages == 0) {
    showMessage("Error", "No image open");
    exit();
}
originalTitle = getTitle();
originalID = getImageID();

// Duplicate for processing
run("Duplicate...", "title=gradient_processing");
processingID = getImageID();

// Pre-processing: Gaussian blur to reduce noise
run("Gaussian Blur...", "sigma=" + GAUSSIAN_SIGMA);

// Method 1: Sobel Edge Detection
selectImage(processingID);
run("Duplicate...", "title=sobel_edges");
sobelID = getImageID();

// Apply Sobel operator
run("Find Edges");
setThreshold(GRADIENT_THRESHOLD, 255);
run("Convert to Mask");

// Clean up edges
run("Close-");
run("Fill Holes");
run("Open");

// Find particles (potential faces)
run("Set Measurements...", "area centroid circularity redirect=None decimal=3");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-" + CIRCULARITY_MAX + 
    " show=Outlines display exclude clear add");

print("Sobel method found " + roiManager("count") + " potential faces");

// Method 2: Gradient Magnitude
selectImage(processingID);
run("Duplicate...", "title=gradient_mag");
gradID = getImageID();

// Calculate gradient using ImageJ's built-in gradient
run("32-bit");
run("Duplicate...", "title=grad_x");
gradXID = getImageID();
run("Convolve...", "text1=[-1 0 1\n-2 0 2\n-1 0 1\n]"); // Sobel X

selectImage(gradID);
run("Duplicate...", "title=grad_y");
gradYID = getImageID();
run("Convolve...", "text1=[-1 -2 -1\n0 0 0\n1 2 1\n]"); // Sobel Y

// Calculate magnitude: sqrt(gx^2 + gy^2)
imageCalculator("Multiply create 32-bit", "grad_x", "grad_x");
rename("gx_squared");
gxSqID = getImageID();

imageCalculator("Multiply create 32-bit", "grad_y", "grad_y");
rename("gy_squared");
gySqID = getImageID();

imageCalculator("Add create 32-bit", "gx_squared", "gy_squared");
rename("gradient_magnitude");
gradMagID = getImageID();

// Take square root
run("Square Root");
run("8-bit");

// Threshold and find contours
setThreshold(GRADIENT_THRESHOLD, 255);
run("Convert to Mask");
run("Invert");
run("Close-");
run("Fill Holes");

// Find particles
roiManager("reset");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-" + CIRCULARITY_MAX + 
    " show=Outlines display exclude clear add");

print("Gradient magnitude method found " + roiManager("count") + " potential faces");

// Method 3: Laplacian of Gaussian (LoG)
selectImage(processingID);
run("Duplicate...", "title=LoG_processing");
logID = getImageID();

// Apply LoG filter
run("FeatureJ Laplacian", "compute smoothing=" + GAUSSIAN_SIGMA);
logResultID = getImageID();

// Find zero crossings (edge pixels)
run("Duplicate...", "title=LoG_edges");
run("8-bit");

// Threshold to find edges
setAutoThreshold("Otsu dark");
run("Convert to Mask");
run("Invert");

// Morphological operations to close contours
run("Dilate");
run("Close-");
run("Fill Holes");
run("Erode");

// Find particles
roiManager("reset");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-" + CIRCULARITY_MAX + 
    " show=Outlines display exclude clear add");

print("Laplacian of Gaussian method found " + roiManager("count") + " potential faces");

// Display results on original image
selectImage(originalID);
roiManager("Show All");
roiManager("Show All with labels");

// Clean up intermediate images
selectImage(processingID); close();
selectImage(sobelID); close();
selectImage(gradID); close();
selectImage(gradXID); close();
selectImage(gradYID); close();
selectImage(gxSqID); close();
selectImage(gySqID); close();
selectImage(gradMagID); close();
selectImage(logID); close();
if (isOpen(logResultID)) { selectImage(logResultID); close(); }

print("=== Edge Detection Complete ===");
print("Check ROI Manager for detected faces");
print("Adjust GRADIENT_THRESHOLD (" + GRADIENT_THRESHOLD + ") if needed");
