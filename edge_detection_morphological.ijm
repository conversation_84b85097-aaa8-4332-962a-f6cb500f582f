// Edge Detection - Morphological Edge Detection
// Uses morphological operations to detect edges and boundaries

// Configuration
STRUCTURING_ELEMENT_SIZE = 3;
GRADIENT_TYPE = "external"; // "external", "internal", "morphological"
MIN_FACE_AREA = 1000;
MAX_FACE_AREA = 50000;
CIRCULARITY_MIN = 0.3;
GAUSSIAN_BLUR = 1.0;

print("\\Clear");
print("=== Morphological Edge Detection ===");

// Get original image
if (nImages == 0) {
    showMessage("Error", "No image open");
    exit();
}
originalTitle = getTitle();
originalID = getImageID();

// Duplicate for processing
run("Duplicate...", "title=morphological_processing");
processingID = getImageID();

// Pre-processing
if (GAUSSIAN_BLUR > 0) {
    run("Gaussian Blur...", "sigma=" + GAUSSIAN_BLUR);
}

// Method 1: Morphological Gradient
selectImage(processingID);
run("Duplicate...", "title=morph_gradient");
gradientID = getImageID();

// Create dilated and eroded versions
run("Duplicate...", "title=dilated");
dilatedID = getImageID();
run("Maximum...", "radius=" + STRUCTURING_ELEMENT_SIZE);

selectImage(gradientID);
run("Duplicate...", "title=eroded");
erodedID = getImageID();
run("Minimum...", "radius=" + STRUCTURING_ELEMENT_SIZE);

// Morphological gradient = dilated - eroded
imageCalculator("Subtract create", "dilated", "eroded");
rename("morphological_gradient");
morphGradID = getImageID();

// Threshold and find contours
setAutoThreshold("Otsu dark");
run("Convert to Mask");
run("Invert");
run("Close-");
run("Fill Holes");

run("Set Measurements...", "area centroid circularity redirect=None decimal=3");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-1.0 show=Outlines display exclude clear add");

print("Morphological gradient found " + roiManager("count") + " potential faces");

// Method 2: Top-Hat Transform
selectImage(processingID);
run("Duplicate...", "title=tophat");
tophatID = getImageID();

// White top-hat (original - opening)
run("Duplicate...", "title=opened");
openedID = getImageID();
run("Minimum...", "radius=" + STRUCTURING_ELEMENT_SIZE);
run("Maximum...", "radius=" + STRUCTURING_ELEMENT_SIZE);

imageCalculator("Subtract create", "tophat", "opened");
rename("white_tophat");
whiteTophatID = getImageID();

// Black top-hat (closing - original)
selectImage(tophatID);
run("Duplicate...", "title=closed");
closedID = getImageID();
run("Maximum...", "radius=" + STRUCTURING_ELEMENT_SIZE);
run("Minimum...", "radius=" + STRUCTURING_ELEMENT_SIZE);

imageCalculator("Subtract create", "closed", "tophat");
rename("black_tophat");
blackTophatID = getImageID();

// Combine both top-hats
imageCalculator("Add create", "white_tophat", "black_tophat");
rename("combined_tophat");
combinedTophatID = getImageID();

setAutoThreshold("Otsu dark");
run("Convert to Mask");
run("Invert");
run("Close-");
run("Fill Holes");

roiManager("reset");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-1.0 show=Outlines display exclude clear add");

print("Top-hat transform found " + roiManager("count") + " potential faces");

// Method 3: Watershed-based Edge Detection
selectImage(processingID);
run("Duplicate...", "title=watershed_prep");
watershedID = getImageID();

// Ensure 8-bit format for watershed
run("8-bit");

// Invert for watershed (peaks become valleys)
run("Invert");

// Apply distance transform
run("Distance Transform Watershed", "distances=[Borgefors (3,4)] output=[16 bits] normalize dynamic=1 connectivity=4");
watershedResultID = getImageID();

// Find watershed lines (edges)
setThreshold(1, 65535);
run("Convert to Mask");
run("Invert");

// Clean up watershed lines
run("Skeletonize");
run("Dilate");
run("Close-");
run("Fill Holes");

roiManager("reset");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-1.0 show=Outlines display exclude clear add");

print("Watershed-based method found " + roiManager("count") + " potential faces");

// Method 4: Hit-or-Miss Transform for specific patterns
selectImage(processingID);
run("Duplicate...", "title=hit_miss");
hitMissID = getImageID();

// Convert to binary first
setAutoThreshold("Otsu dark");
run("Convert to Mask");

// Apply hit-or-miss for corner detection
// This is a simplified version - you might need specific kernels for hexagonal patterns
run("Duplicate...", "title=corners");
cornersID = getImageID();

// Simple corner detection kernel
run("Convolve...", "text1=[1 -1 -1\n-1 1 -1\n-1 -1 1\n]");
setThreshold(128, 255);
run("Convert to Mask");

// Dilate corners to create regions
run("Maximum...", "radius=10");
run("Close-");
run("Fill Holes");

roiManager("reset");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-1.0 show=Outlines display exclude clear add");

print("Hit-or-Miss transform found " + roiManager("count") + " potential faces");

// Display results
selectImage(originalID);
roiManager("Show All");
roiManager("Show All with labels");

// Clean up
selectImage(processingID); close();
selectImage(gradientID); close();
selectImage(dilatedID); close();
selectImage(erodedID); close();
selectImage(morphGradID); close();
selectImage(tophatID); close();
selectImage(openedID); close();
selectImage(whiteTophatID); close();
selectImage(closedID); close();
selectImage(blackTophatID); close();
selectImage(combinedTophatID); close();
selectImage(watershedID); close();
if (isOpen(watershedResultID)) { selectImage(watershedResultID); close(); }
selectImage(hitMissID); close();
selectImage(cornersID); close();

print("=== Morphological Edge Detection Complete ===");
print("Adjust STRUCTURING_ELEMENT_SIZE (" + STRUCTURING_ELEMENT_SIZE + ") if needed");
