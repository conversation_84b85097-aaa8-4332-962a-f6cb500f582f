// Edge Detection - Local Variance Based Face Finder
// Uses local variance and standard deviation to detect edges

// Configuration
WINDOW_SIZE = 5;  // Size of local neighborhood
VARIANCE_THRESHOLD = 100;
MIN_FACE_AREA = 1000;
MAX_FACE_AREA = 50000;
CIRCULARITY_MIN = 0.3;
GAUSSIAN_BLUR = 1.0;

print("\\Clear");
print("=== Local Variance-Based Edge Detection ===");

// Get original image
if (nImages == 0) {
    showMessage("Error", "No image open");
    exit();
}
originalTitle = getTitle();
originalID = getImageID();

// Duplicate for processing
run("Duplicate...", "title=variance_processing");
processingID = getImageID();

// Pre-processing
if (GAUSSIAN_BLUR > 0) {
    run("Gaussian Blur...", "sigma=" + GAUSSIAN_BLUR);
}

// Method 1: Local Standard Deviation
selectImage(processingID);
run("Duplicate...", "title=local_std");
stdID = getImageID();

// Apply local standard deviation filter
run("Filters", "filter=[Standard Deviation] radius=" + floor(WINDOW_SIZE/2));
run("8-bit");

// Threshold high variance areas (edges)
setThreshold(VARIANCE_THRESHOLD, 255);
run("Convert to Mask");
run("Invert");  // Make edges white

// Morphological operations
run("Close-");
run("Fill Holes");
run("Open");

// Find contours
run("Set Measurements...", "area centroid circularity redirect=None decimal=3");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-1.0 show=Outlines display exclude clear add");

print("Local standard deviation found " + roiManager("count") + " potential faces");

// Method 2: Range Filter (max - min in neighborhood)
selectImage(processingID);
run("Duplicate...", "title=range_filter");
rangeID = getImageID();

// Create maximum and minimum filtered images
run("Duplicate...", "title=max_filtered");
maxID = getImageID();
run("Maximum...", "radius=" + floor(WINDOW_SIZE/2));

selectImage(rangeID);
run("Duplicate...", "title=min_filtered");
minID = getImageID();
run("Minimum...", "radius=" + floor(WINDOW_SIZE/2));

// Calculate range (max - min)
imageCalculator("Subtract create", "max_filtered", "min_filtered");
rename("range_image");
rangeResultID = getImageID();

// Threshold and process
run("8-bit");
setThreshold(VARIANCE_THRESHOLD/2, 255);  // Use lower threshold for range
run("Convert to Mask");
run("Invert");

// Clean up
run("Close-");
run("Fill Holes");
run("Open");

// Find particles
roiManager("reset");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-1.0 show=Outlines display exclude clear add");

print("Range filter found " + roiManager("count") + " potential faces");

// Method 3: Coefficient of Variation
selectImage(processingID);
run("Duplicate...", "title=coeff_var");
cvID = getImageID();

// Calculate local mean
run("Duplicate...", "title=local_mean");
meanID = getImageID();
run("Mean...", "radius=" + floor(WINDOW_SIZE/2));

// Calculate local variance using (mean of squares) - (square of mean)
selectImage(cvID);
run("Duplicate...", "title=squared");
squaredID = getImageID();
run("Square");
run("Mean...", "radius=" + floor(WINDOW_SIZE/2));

// Variance = E[X²] - (E[X])²
selectImage(meanID);
run("Duplicate...", "title=mean_squared");
meanSqID = getImageID();
run("Square");

imageCalculator("Subtract create 32-bit", "squared", "mean_squared");
rename("variance_map");
varID = getImageID();

// Coefficient of variation = sqrt(variance) / mean
run("Square Root");
imageCalculator("Divide create 32-bit", "variance_map", "local_mean");
rename("coeff_variation");
cvResultID = getImageID();

// Threshold coefficient of variation
run("8-bit");
setThreshold(50, 255);  // Adjust based on your images
run("Convert to Mask");
run("Invert");

// Clean up
run("Close-");
run("Fill Holes");

// Find particles
roiManager("reset");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-1.0 show=Outlines display exclude clear add");

print("Coefficient of variation found " + roiManager("count") + " potential faces");

// Display results
selectImage(originalID);
roiManager("Show All");
roiManager("Show All with labels");

// Clean up
selectImage(processingID); close();
selectImage(stdID); close();
selectImage(rangeID); close();
selectImage(maxID); close();
selectImage(minID); close();
selectImage(rangeResultID); close();
selectImage(cvID); close();
selectImage(meanID); close();
selectImage(squaredID); close();
selectImage(meanSqID); close();
selectImage(varID); close();
selectImage(cvResultID); close();

print("=== Local Variance Detection Complete ===");
print("Adjust VARIANCE_THRESHOLD (" + VARIANCE_THRESHOLD + ") if needed");
