// Edge Detection - Directional Edge Detection
// Uses directional filters and Canny-like edge detection

// Configuration
GAUSSIAN_SIGMA = 1.5;
LOW_THRESHOLD = 10;
HIGH_THRESHOLD = 30;
MIN_FACE_AREA = 1000;
MAX_FACE_AREA = 50000;
CIRCULARITY_MIN = 0.3;

print("\\Clear");
print("=== Directional Edge Detection ===");

// Get original image
if (nImages == 0) {
    showMessage("Error", "No image open");
    exit();
}
originalTitle = getTitle();
originalID = getImageID();

// Duplicate for processing
run("Duplicate...", "title=directional_processing");
processingID = getImageID();

// Pre-processing
run("Gaussian Blur...", "sigma=" + GAUSSIAN_SIGMA);

// Method 1: Multi-directional Sobel
selectImage(processingID);
run("32-bit");

// Create directional edge maps
directions = newArray("0°", "45°", "90°", "135°");
kernels = newArray(
    "[-1 0 1\n-2 0 2\n-1 0 1\n]",      // 0° (vertical edges)
    "[-2 -1 0\n-1 0 1\n0 1 2\n]",       // 45°
    "[-1 -2 -1\n0 0 0\n1 2 1\n]",       // 90° (horizontal edges)
    "[0 1 2\n-1 0 1\n-2 -1 0\n]"        // 135°
);

edgeImages = newArray(4);
for (i = 0; i < 4; i++) {
    selectImage(processingID);
    run("Duplicate...", "title=edge_" + directions[i]);
    edgeImages[i] = getImageID();
    run("Convolve...", "text1=" + kernels[i]);
    run("Abs");
}

// Combine all directional edges
selectImage(edgeImages[0]);
for (i = 1; i < 4; i++) {
    imageCalculator("Max", "edge_" + directions[0], "edge_" + directions[i]);
}
rename("combined_edges");
combinedID = getImageID();

// Apply double thresholding (Canny-like)
run("8-bit");
run("Duplicate...", "title=strong_edges");
strongID = getImageID();
setThreshold(HIGH_THRESHOLD, 255);
run("Convert to Mask");

selectImage(combinedID);
run("Duplicate...", "title=weak_edges");
weakID = getImageID();
setThreshold(LOW_THRESHOLD, 255);
run("Convert to Mask");

// Hysteresis: connect weak edges to strong edges
imageCalculator("OR", "weak_edges", "strong_edges");
selectImage(weakID);
run("Dilate");
imageCalculator("AND", "weak_edges", "strong_edges");

// Clean up and find contours
run("Close-");
run("Fill Holes");

run("Set Measurements...", "area centroid circularity redirect=None decimal=3");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-1.0 show=Outlines display exclude clear add");

print("Directional Sobel found " + roiManager("count") + " potential faces");

// Method 2: Prewitt Operators
selectImage(processingID);
run("Duplicate...", "title=prewitt_x");
prewittXID = getImageID();
run("Convolve...", "text1=[-1 0 1\n-1 0 1\n-1 0 1\n]");

selectImage(processingID);
run("Duplicate...", "title=prewitt_y");
prewittYID = getImageID();
run("Convolve...", "text1=[-1 -1 -1\n0 0 0\n1 1 1\n]");

// Combine Prewitt responses
imageCalculator("Add create", "prewitt_x", "prewitt_y");
rename("prewitt_combined");
prewittCombID = getImageID();

run("8-bit");
setThreshold(HIGH_THRESHOLD, 255);
run("Convert to Mask");
run("Invert");
run("Close-");
run("Fill Holes");

roiManager("reset");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-1.0 show=Outlines display exclude clear add");

print("Prewitt operators found " + roiManager("count") + " potential faces");

// Method 3: Roberts Cross-Gradient
selectImage(processingID);
run("Duplicate...", "title=roberts_1");
roberts1ID = getImageID();
run("Convolve...", "text1=[1 0\n0 -1\n]");

selectImage(processingID);
run("Duplicate...", "title=roberts_2");
roberts2ID = getImageID();
run("Convolve...", "text1=[0 1\n-1 0\n]");

// Combine Roberts responses
imageCalculator("Add create", "roberts_1", "roberts_2");
rename("roberts_combined");
robertsCombID = getImageID();

run("8-bit");
setThreshold(HIGH_THRESHOLD, 255);
run("Convert to Mask");
run("Invert");
run("Close-");
run("Fill Holes");

roiManager("reset");
run("Analyze Particles...", "size=" + MIN_FACE_AREA + "-" + MAX_FACE_AREA + 
    " circularity=" + CIRCULARITY_MIN + "-1.0 show=Outlines display exclude clear add");

print("Roberts cross-gradient found " + roiManager("count") + " potential faces");

// Display results
selectImage(originalID);
roiManager("Show All");
roiManager("Show All with labels");

// Clean up
selectImage(processingID); close();
for (i = 0; i < 4; i++) {
    selectImage(edgeImages[i]); close();
}
selectImage(combinedID); close();
selectImage(strongID); close();
selectImage(weakID); close();
selectImage(prewittXID); close();
selectImage(prewittYID); close();
selectImage(prewittCombID); close();
selectImage(roberts1ID); close();
selectImage(roberts2ID); close();
selectImage(robertsCombID); close();

print("=== Directional Edge Detection Complete ===");
print("Adjust thresholds if needed:");
print("LOW_THRESHOLD: " + LOW_THRESHOLD);
print("HIGH_THRESHOLD: " + HIGH_THRESHOLD);
